#root {
  padding: 1rem;
  width: 100%;
}

/* Widget catalog improvements */
.widgets-catalog {
  background-color: #fff;
}

.widget-item {
  transition: all 0.2s ease;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.widget-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #3498db;
}

/* Grid layout improvements */
.react-grid-item {
  border-radius: 16px;
  border: 2px solid #e9ecef;
  background-color: #fff;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
  cursor: move;
}

.react-grid-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: #3498db;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 1000;
  opacity: 0.8;
  transform: rotate(3deg) scale(1.05);
  border-color: #2980b9;
}

.react-grid-item .widget-body {
  pointer-events: auto;
  cursor: default;
}

.react-grid-item .widget-header {
  pointer-events: auto;
  cursor: move;
}

.react-grid-item.react-grid-placeholder {
  background: linear-gradient(
    135deg,
    rgba(52, 152, 219, 0.1) 0%,
    rgba(52, 152, 219, 0.2) 100%
  );
  border: 3px solid #3498db;
  border-radius: 16px;
  animation: pulse-placeholder 1.5s ease-in-out infinite;
}

@keyframes pulse-placeholder {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* Grid Widget Container */
.grid-widget-container {
  height: 100%;
  width: 100%;
  position: relative;
  container-type: size;
}

.grid-widget-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 14px;
  overflow: hidden;
  position: relative;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(8px, 2vw, 16px) clamp(12px, 3vw, 20px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  cursor: move;
  user-select: none;
  min-height: clamp(40px, 8vh, 60px);
}

.widget-header.drag-handle {
  cursor: move;
}

.widget-header:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.widget-title {
  display: flex;
  align-items: center;
  gap: clamp(4px, 1vw, 12px);
  font-weight: 600;
  font-size: clamp(12px, 2.5vw, 18px);
  flex: 1;
  line-height: 1.2;
}

.widget-icon {
  font-size: clamp(14px, 2vw, 20px);
  opacity: 0.9;
}

.widget-name {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.drag-indicator {
  font-size: 12px;
  opacity: 0.7;
  margin-left: 8px;
  letter-spacing: -2px;
  transition: opacity 0.2s ease;
}

.widget-header:hover .drag-indicator {
  opacity: 1;
}

.widget-actions {
  display: flex;
  gap: 4px;
  pointer-events: auto;
  z-index: 10;
  position: relative;
}

.widget-action-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
  width: clamp(20px, 3vw, 28px) !important;
  height: clamp(20px, 3vw, 28px) !important;
  border-radius: clamp(4px, 1vw, 8px) !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: clamp(10px, 1.5vw, 14px) !important;
  font-weight: bold !important;
  transition: all 0.2s ease !important;
  backdrop-filter: blur(10px) !important;
  pointer-events: auto !important;
  z-index: 1001 !important;
  position: relative !important;
}

.widget-action-btn:hover {
  background: rgba(255, 255, 255, 0.4) !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.widget-body {
  flex: 1;
  padding: clamp(8px, 2vw, 24px);
  overflow: auto;
  background: #fff;
  position: relative;
  cursor: default;
  pointer-events: auto;
  font-size: clamp(12px, 1.5vw, 16px);
  line-height: 1.4;
}

.widget-body * {
  pointer-events: auto !important;
}

.widget-body button {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 10;
  position: relative;
}

/* React Grid Layout Resize Handle Styles */
.react-resizable-handle {
  position: absolute !important;
  width: 20px !important;
  height: 20px !important;
  bottom: 0 !important;
  right: 0 !important;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  border-radius: 16px 0 14px 0 !important;
  cursor: se-resize !important;
  z-index: 1000 !important;
  opacity: 0.8 !important;
  transition: all 0.2s ease !important;
  pointer-events: auto !important;
}

.react-resizable-handle:hover {
  opacity: 1 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4) !important;
}

.react-resizable-handle::after {
  content: "⋱" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  pointer-events: none !important;
}

/* Ensure resize handles are visible on grid items */
.react-grid-item .react-resizable-handle {
  display: block !important;
  visibility: visible !important;
}

.widget-body:hover {
  background: #fafbfc;
}

/* Responsive Widget Content Utilities */
.widget-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: clamp(8px, 2vh, 16px);
}

.widget-content h1,
.widget-content h2,
.widget-content h3,
.widget-content h4,
.widget-content h5,
.widget-content h6 {
  margin: 0 0 clamp(4px, 1vh, 12px) 0;
  font-size: clamp(14px, 3vw, 24px);
  line-height: 1.2;
}

.widget-content p {
  margin: 0 0 clamp(4px, 1vh, 8px) 0;
  font-size: clamp(12px, 1.5vw, 16px);
  line-height: 1.4;
}

.widget-content button {
  padding: clamp(4px, 1vh, 12px) clamp(8px, 2vw, 16px);
  font-size: clamp(11px, 1.5vw, 14px);
  border-radius: clamp(3px, 0.5vw, 6px);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: clamp(28px, 4vh, 40px);
}

.widget-content input {
  padding: clamp(4px, 1vh, 8px) clamp(6px, 1.5vw, 12px);
  font-size: clamp(11px, 1.5vw, 14px);
  border: 1px solid #ddd;
  border-radius: clamp(3px, 0.5vw, 6px);
  min-height: clamp(24px, 3vh, 32px);
}

.widget-display-value {
  font-size: clamp(16px, 4vw, 32px);
  font-weight: bold;
  padding: clamp(8px, 2vh, 16px);
  border-radius: clamp(4px, 1vw, 8px);
  text-align: center;
  min-height: clamp(40px, 6vh, 80px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.widget-list-item {
  padding: clamp(4px, 1vh, 8px);
  margin: clamp(2px, 0.5vh, 4px) 0;
  border-radius: clamp(3px, 0.5vw, 6px);
  font-size: clamp(11px, 1.5vw, 14px);
}

.widget-button-group {
  display: flex;
  gap: clamp(4px, 1vw, 8px);
  flex-wrap: wrap;
  justify-content: center;
}

.widget-input-group {
  display: flex;
  gap: clamp(4px, 1vw, 8px);
  align-items: center;
  flex-wrap: wrap;
}

/* Container Query Responsive Styles */
@container (max-width: 200px) {
  .widget-content h3 {
    font-size: 14px;
  }

  .widget-display-value {
    font-size: 16px;
    padding: 6px;
    min-height: 30px;
  }

  .widget-content button {
    padding: 4px 6px;
    font-size: 10px;
    min-height: 24px;
  }

  .widget-content input {
    padding: 3px 6px;
    font-size: 10px;
    min-height: 20px;
  }

  .widget-list-item {
    padding: 3px;
    font-size: 10px;
  }
}

@container (min-width: 200px) and (max-width: 300px) {
  .widget-content h3 {
    font-size: 16px;
  }

  .widget-display-value {
    font-size: 20px;
    padding: 8px;
    min-height: 40px;
  }

  .widget-content button {
    padding: 6px 10px;
    font-size: 12px;
    min-height: 28px;
  }

  .widget-content input {
    padding: 5px 8px;
    font-size: 12px;
    min-height: 24px;
  }
}

@container (min-width: 300px) {
  .widget-content h3 {
    font-size: 18px;
  }

  .widget-display-value {
    font-size: 24px;
    padding: 12px;
    min-height: 50px;
  }

  .widget-content button {
    padding: 8px 14px;
    font-size: 14px;
    min-height: 32px;
  }

  .widget-content input {
    padding: 6px 10px;
    font-size: 14px;
    min-height: 28px;
  }
}

/* Height-based container queries */
@container (max-height: 150px) {
  .widget-content {
    gap: 4px;
  }

  .widget-content h3 {
    margin-bottom: 2px;
    font-size: 12px;
  }

  .widget-display-value {
    min-height: 25px;
    font-size: 14px;
    padding: 4px;
  }

  .widget-content button {
    min-height: 20px;
    padding: 2px 6px;
    font-size: 10px;
  }
}

@container (min-height: 150px) and (max-height: 250px) {
  .widget-content {
    gap: 6px;
  }

  .widget-content h3 {
    margin-bottom: 4px;
  }
}

@container (min-height: 250px) {
  .widget-content {
    gap: 12px;
  }

  .widget-content h3 {
    margin-bottom: 8px;
  }
}

/* Layout Container */
.layout-container {
  height: 100%;
  width: 100%;
  min-height: 760px;
  display: flex;
  flex-direction: column;
}

/* Grid Layout Container */
.layout-container .layout-grid {
  height: 100%;
  width: 100%;
  min-height: 760px;
  display: flex;
  flex-direction: column;
}

/* React Grid Layout Container */
.layout-container .react-grid-layout {
  height: 100% !important;
  min-height: 760px !important;
  width: 100% !important;
  flex: 1;
  position: relative;
}

/* Empty grid layout styling */
.layout-container .react-grid-layout:empty::before {
  content: "🎯 Drop widgets here to start building your layout";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  color: #6c757d;
  text-align: center;
  pointer-events: none;
  z-index: 1;
}

/* Grid layout with items */
.layout-container .react-grid-layout:not(:empty)::before {
  display: none;
}

/* Ensure all layout children take full height */
.layout-container > div,
.layout-grid,
.layout-grid > div {
  height: 100%;
  min-height: 760px;
}

/* Drop zone highlighting */
.layout-area-dragover {
  border-color: #27ae60 !important;
  background-color: rgba(39, 174, 96, 0.05) !important;
  animation: pulse-border 1s infinite;
}

@keyframes pulse-border {
  0% {
    border-color: #27ae60;
  }
  50% {
    border-color: #2ecc71;
  }
  100% {
    border-color: #27ae60;
  }
}

/* Layout Controls Styles */
.layout-controls-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16px;
  padding: 0;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.layout-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
}

.layout-controls-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.layout-controls-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 12px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.layout-controls-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
}

.layout-controls-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  opacity: 0.9;
  font-weight: 400;
}

.layout-status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  font-size: 14px;
  font-weight: 500;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: #27ae60;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.layout-controls-content {
  padding: 28px;
}

.layout-controls-section {
  margin-bottom: 24px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.label-icon {
  font-size: 18px;
}

.select-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  max-width: 320px;
}

.layout-type-select {
  width: 100%;
  padding: 14px 16px;
  padding-right: 48px;
  font-size: 15px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background-color: #fff;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.layout-type-select:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.layout-type-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.1);
}

.select-arrow {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #6c757d;
  transition: all 0.3s ease;
}

.layout-type-select:focus + .select-arrow {
  color: #3498db;
  transform: translateY(-50%) rotate(180deg);
}

.layout-controls-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.control-button {
  padding: 14px 20px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;
}

.control-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.control-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.control-button:not(:disabled):active {
  transform: translateY(0);
}

.button-icon {
  font-size: 16px;
}

.button-text {
  font-weight: 600;
}

.button-badge {
  background: rgba(255, 255, 255, 0.3);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
}

.control-button--danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.control-button--danger:not(:disabled):hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
}

.control-button--success {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.control-button--success:not(:disabled):hover {
  background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
}

.control-button--info {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.control-button--info:not(:disabled):hover {
  background: linear-gradient(135deg, #2980b9 0%, #21618c 100%);
}

/* Layout Placeholder Styles */
.layout-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 2px dashed #dee2e6;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.layout-placeholder:hover {
  border-color: #3498db;
  background: linear-gradient(135deg, #f8f9fa 0%, #f0f8ff 100%);
}

.placeholder-content {
  text-align: center;
  max-width: 400px;
  padding: 40px 20px;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 24px;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.placeholder-title {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.placeholder-description {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #6c757d;
  line-height: 1.5;
}

.placeholder-features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(52, 152, 219, 0.05);
  border: 1px solid rgba(52, 152, 219, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: #495057;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(52, 152, 219, 0.1);
  border-color: rgba(52, 152, 219, 0.2);
  transform: translateX(4px);
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

/* Responsive improvements */
@media (max-width: 1200px) {
  .react-grid-item {
    font-size: 14px;
  }

  .layout-controls-header {
    flex-direction: column;
    gap: 16px;
    align-items: center;
    text-align: center;
    padding: 20px 24px;
  }

  .layout-controls-title-section {
    flex-direction: column;
    gap: 12px;
  }

  .layout-controls-actions {
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
  }

  .select-wrapper {
    max-width: 280px;
  }

  .widget-header {
    padding: 10px 14px;
  }

  .widget-title {
    font-size: 13px;
  }

  .widget-body {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .react-grid-item {
    font-size: 12px;
    padding: 8px;
  }

  .layout-controls-container {
    margin: 0 -8px;
  }

  .layout-controls-content {
    padding: 20px;
  }

  .layout-controls-header {
    padding: 16px 20px;
  }

  .layout-controls-icon {
    font-size: 28px;
    padding: 10px;
  }

  .layout-controls-title {
    font-size: 20px;
  }

  .layout-controls-subtitle {
    font-size: 13px;
  }

  .layout-status-badge {
    padding: 6px 12px;
    font-size: 13px;
  }

  .layout-controls-actions {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .control-button {
    width: 100%;
    min-width: auto;
    padding: 12px 16px;
  }

  .select-wrapper {
    max-width: 100%;
  }

  .layout-type-select {
    padding: 12px 14px;
    padding-right: 44px;
    font-size: 14px;
  }

  .control-label {
    font-size: 15px;
  }

  .widget-header {
    padding: 8px 12px;
  }

  .widget-title {
    font-size: 12px;
  }

  .widget-icon {
    font-size: 14px;
  }

  .widget-body {
    padding: 10px;
  }

  .widget-action-btn {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .layout-controls-header {
    padding: 12px 16px;
  }

  .layout-controls-content {
    padding: 16px;
  }

  .layout-controls-title-section {
    gap: 8px;
  }

  .layout-controls-icon {
    font-size: 24px;
    padding: 8px;
  }

  .layout-controls-title {
    font-size: 18px;
  }

  .button-text {
    font-size: 13px;
  }

  .button-badge {
    font-size: 11px;
    padding: 1px 6px;
  }
}
