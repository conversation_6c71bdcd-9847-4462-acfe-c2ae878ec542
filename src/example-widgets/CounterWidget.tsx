import React, { useState } from "react";

interface CounterWidgetProps {
  initialValue?: number;
  step?: number;
}

export const CounterWidget: React.FC<CounterWidgetProps> = ({
  initialValue = 0,
  step = 1,
}) => {
  const [count, setCount] = useState(initialValue);

  return (
    <div
      style={{
        padding: "20px",
        borderRadius: "8px",
        textAlign: "center",
        backgroundColor: "#f8f9fa",
        width: "100%",
        height: "100%",
      }}
    >
      <h3>Counter Widget</h3>
      <div
        style={{
          fontSize: "32px",
          margin: "15px 0",
          fontWeight: "bold",
          color: "#fff",
          border: "3px solid #007bff",
          padding: "15px",
          borderRadius: "8px",
          backgroundColor: "#007bff",
          textAlign: "center",
          minHeight: "60px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        COUNT: {count}
      </div>
      <div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setCount(count - step);
          }}
          style={{
            margin: "0 5px",
            padding: "8px 16px",
            backgroundColor: "#dc3545",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          -
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setCount(count + step);
          }}
          style={{
            margin: "0 5px",
            padding: "8px 16px",
            backgroundColor: "#28a745",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          +
        </button>
      </div>
    </div>
  );
};
