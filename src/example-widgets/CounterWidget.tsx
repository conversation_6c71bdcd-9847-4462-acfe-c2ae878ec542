import React, { useState } from "react";

interface CounterWidgetProps {
  initialValue?: number;
  step?: number;
}

export const CounterWidget: React.FC<CounterWidgetProps> = ({
  initialValue = 0,
  step = 1,
}) => {
  const [count, setCount] = useState(initialValue);

  return (
    <div className="widget-content" style={{ textAlign: "center", backgroundColor: "#f8f9fa" }}>
      <h3>Counter Widget</h3>
      <div
        className="widget-display-value"
        style={{
          color: "#fff",
          border: "3px solid #007bff",
          backgroundColor: "#007bff",
        }}
      >
        COUNT: {count}
      </div>
      <div className="widget-button-group">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setCount(count - step);
          }}
          style={{
            backgroundColor: "#dc3545",
            color: "white",
          }}
        >
          -
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setCount(count + step);
          }}
          style={{
            backgroundColor: "#28a745",
            color: "white",
          }}
        >
          +
        </button>
      </div>
    </div>
  );
};
