import React, { useState } from "react";

interface TodoItem {
  id: number;
  text: string;
  completed: boolean;
}

export const TodoWidget: React.FC = () => {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [inputValue, setInputValue] = useState("");

  const addTodo = () => {
    if (inputValue.trim()) {
      setTodos([
        ...todos,
        {
          id: Date.now(),
          text: inputValue.trim(),
          completed: false,
        },
      ]);
      setInputValue("");
    }
  };

  const toggleTodo = (id: number) => {
    setTodos(
      todos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  };

  const deleteTodo = (id: number) => {
    setTodos(todos.filter((todo) => todo.id !== id));
  };

  return (
    <div className="widget-content" style={{ backgroundColor: "#f8f9fa" }}>
      <h3>Todo Widget</h3>
      <div className="widget-input-group">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && addTodo()}
          placeholder="Add a new todo..."
          style={{ flex: 1, minWidth: "120px" }}
        />
        <button
          onClick={addTodo}
          style={{
            backgroundColor: "#ffc107",
            color: "black",
            flexShrink: 0,
          }}
        >
          Add
        </button>
      </div>
      <div style={{ flex: 1, overflow: "auto" }}>
        {todos.map((todo) => (
          <div
            key={todo.id}
            className="widget-list-item"
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: todo.completed ? "#e9ecef" : "white",
              gap: "clamp(4px, 1vw, 8px)",
            }}
          >
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
              style={{ flexShrink: 0 }}
            />
            <span
              style={{
                flex: 1,
                textDecoration: todo.completed ? "line-through" : "none",
                color: todo.completed ? "#6c757d" : "black",
                wordBreak: "break-word",
              }}
            >
              {todo.text}
            </span>
            <button
              onClick={() => deleteTodo(todo.id)}
              style={{
                backgroundColor: "#dc3545",
                color: "white",
                flexShrink: 0,
                padding: "clamp(2px, 0.5vh, 6px) clamp(4px, 1vw, 8px)",
                fontSize: "clamp(10px, 1.2vw, 12px)",
              }}
            >
              Delete
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
