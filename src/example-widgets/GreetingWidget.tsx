import React, { useState } from "react";

interface GreetingWidgetProps {
  defaultName?: string;
}

export const GreetingWidget: React.FC<GreetingWidgetProps> = ({
  defaultName = "World",
}) => {
  const [name, setName] = useState(defaultName);

  return (
    <div
      style={{
        padding: "20px",
        borderRadius: "8px",
        backgroundColor: "#f8f9fa",
        width: "100%",
        height: "100%",
      }}
    >
      <h3>Greeting Widget</h3>
      <div style={{ margin: "10px 0" }}>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter your name"
          style={{
            padding: "8px",
            border: "1px solid #ddd",
            borderRadius: "4px",
            marginRight: "10px",
            width: "200px",
          }}
        />
      </div>
      <div
        style={{
          fontSize: "18px",
          fontWeight: "bold",
          color: "#28a745",
          margin: "10px 0",
        }}
      >
        Hello, {name}! 👋
      </div>
    </div>
  );
};
