import React, { useState } from "react";

interface GreetingWidgetProps {
  defaultName?: string;
}

export const GreetingWidget: React.FC<GreetingWidgetProps> = ({
  defaultName = "World",
}) => {
  const [name, setName] = useState(defaultName);

  return (
    <div className="widget-content" style={{ backgroundColor: "#f8f9fa" }}>
      <h3>Greeting Widget</h3>
      <div className="widget-input-group">
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter your name"
          style={{ flex: 1, minWidth: "120px" }}
        />
      </div>
      <div
        className="widget-display-value"
        style={{
          color: "#28a745",
          backgroundColor: "rgba(40, 167, 69, 0.1)",
          border: "2px solid #28a745",
        }}
      >
        Hello, {name}! 👋
      </div>
    </div>
  );
};
