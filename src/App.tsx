import { useState } from "react";
import { WidgetsProvider, WidgetsCatalog, Layout } from "./lib";
import type { Widget, PlacedWidget } from "./lib";
import type { Layout as RGLLayout } from "react-grid-layout";
import "./App.css";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import { CounterWidget } from "./example-widgets/CounterWidget";
import { GreetingWidget } from "./example-widgets/GreetingWidget";
import { TodoWidget } from "./example-widgets/TodoWidget";

// Define example widgets
const exampleWidgets: Widget[] = [
  {
    id: "counter",
    name: "Counter Widget",
    description: "A simple counter that can increment and decrement values",
    component: CounterWidget,
    props: { initialValue: 0, step: 1 },
    layout: { w: 3, h: 4, x: 0, y: 0 },
  },
  {
    id: "greeting",
    name: "Greeting Widget",
    description: "A personalized greeting widget with name input",
    component: GreetingWidget,
    props: { defaultName: "Developer" },
    layout: { w: 4, h: 3, x: 3, y: 0 },
  },
  {
    id: "todo",
    name: "Todo List Widget",
    description: "A simple todo list to manage tasks",
    component: TodoWidget,
    layout: { w: 5, h: 6, x: 7, y: 0 },
  },
];

function App() {
  const [currentLayout, setCurrentLayout] = useState<RGLLayout[]>([]);
  const [dropedWidgets, setDropedWidgets] = useState<PlacedWidget[]>([]);



  const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
    console.log("Drop event:", { layout, item, e });

    // Get the drag data from the event
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData("application/json");

    if (dragData) {
      try {
        const data = JSON.parse(dragData);
        console.log(data, "dropped widget data");

        if (data.type === 'widget' && data.widgetId) {
          // Find the original widget by ID from the exampleWidgets array
          const originalWidget = exampleWidgets.find(w => w.id === data.widgetId);

          if (originalWidget) {
            // Generate unique ID for this widget instance
            const instanceId = `${originalWidget.id}-${Date.now()}`;

            // Create layout item for the dropped widget
            const newLayoutItem: RGLLayout = {
              i: instanceId,
              x: item.x,
              y: item.y,
              w: originalWidget.layout?.w || 4,
              h: originalWidget.layout?.h || 4,
              minW: originalWidget.layout?.minW,
              minH: originalWidget.layout?.minH,
              maxW: originalWidget.layout?.maxW,
              maxH: originalWidget.layout?.maxH,
              static: originalWidget.layout?.static || false,
              isDraggable: originalWidget.layout?.isDraggable !== false,
              isResizable: originalWidget.layout?.isResizable !== false,
            };

            // Create placed widget
            const newPlacedWidget: PlacedWidget = {
              id: instanceId,
              widget: originalWidget,
              layout: newLayoutItem,
            };

            // Update states
            setDropedWidgets(prev => [...prev, newPlacedWidget]);
            setCurrentLayout(prev => [...prev, newLayoutItem]);
          } else {
            console.error('Widget not found with ID:', data.widgetId);
          }
        }
      } catch (error) {
        console.error("Error parsing drag data:", error);
      }
    }
  };

  const handleLayoutChange = (layout: RGLLayout[]) => {
    console.log(layout, "layout ");
    setCurrentLayout(layout);

    // Update placed widgets with new layout positions
    setDropedWidgets(prev =>
      prev.map(placedWidget => {
        const updatedLayoutItem = layout.find(item => item.i === placedWidget.id);
        if (updatedLayoutItem) {
          return {
            ...placedWidget,
            layout: updatedLayoutItem,
          };
        }
        return placedWidget;
      })
    );
  };

  const handleDrag = (widget: Widget) => {
    console.log("Dragging widget:", widget.name);
  };

  const removeWidget = (widgetId: string) => {
    setDropedWidgets(prev => prev.filter(w => w.id !== widgetId));
    setCurrentLayout(prev => prev.filter(item => item.i !== widgetId));
  };

  return (
    <WidgetsProvider initialWidgets={exampleWidgets}>
      <div
        style={{
          minHeight: "100vh",
          width: "100%",
        }}
      >
        <div style={{ display: "flex", gap: "24px", minHeight: "1000px" }}>
          {/* Widget Catalog */}
          <div
            style={{
              width: "350px",
              flexShrink: 0,
              backgroundColor: "#fff",
              border: "1px solid #e0e0e0",
              borderRadius: "12px",
              padding: "20px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              height: "fit-content",
            }}
          >
            <div style={{ marginBottom: "16px" }}>
              <h3
                style={{
                  margin: "0 0 8px 0",
                  fontSize: "20px",
                  fontWeight: "600",
                  color: "#2c3e50",
                }}
              >
                🧩 Widget Catalog
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: "#7f8c8d",
                  margin: 0,
                  lineHeight: "1.4",
                }}
              >
                Drag widgets to the layout area to add them →
              </p>
            </div>
            <WidgetsCatalog
              widgets={exampleWidgets}
              onDrag={handleDrag}
              enableDrag={true}
            />
          </div>

          {/* Layout Area */}
          <div
            style={{
              flex: 1,
              border: "2px dashed #3498db",
              borderRadius: "16px",
              minHeight: "900px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "#fff",
              boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                minHeight: "800px",
                padding: "20px",
                backgroundColor: "#fafbfc",
                overflow: "hidden",
              }}
            >
              <div className="layout-container" style={{ position: "relative" }}>
                <Layout
                  onDrop={handleDrop}
                  onLayoutChange={handleLayoutChange}
                  layout={currentLayout}
                  cols={12}
                  rowHeight={50} // Even larger row height for better visibility
                  width={1400} // Increased width for more space
                  margin={[16, 16]} // Larger margins
                  containerPadding={[24, 24]} // More padding
                  isDraggable={true}
                  isResizable={true}
                  isDroppable={true}
                >
                  {dropedWidgets.map((placedWidget) => {
                    const WidgetComponent = placedWidget.widget.component;

                    // Safety check for component
                    if (!WidgetComponent) {
                      console.error('Widget component is undefined for widget:', placedWidget.widget);
                      return null;
                    }

                    return (
                      <div
                        key={placedWidget.id}
                        style={{
                          width: "100%",
                          height: "100%",
                          overflow: "hidden",
                          backgroundColor: "#fff",
                          borderRadius: "8px",
                          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                          position: "relative",
                        }}
                      >
                        {/* Remove button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeWidget(placedWidget.id);
                          }}
                          style={{
                            position: "absolute",
                            top: "4px",
                            right: "4px",
                            width: "24px",
                            height: "24px",
                            borderRadius: "50%",
                            border: "none",
                            backgroundColor: "#dc3545",
                            color: "white",
                            cursor: "pointer",
                            fontSize: "12px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            zIndex: 1000,
                            boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
                          }}
                          title="Remove widget"
                        >
                          ×
                        </button>
                        <WidgetComponent {...(placedWidget.widget.props || {})} />
                      </div>
                    );
                  })}
                </Layout>
              </div>
            </div>
          </div>
        </div>
      </div>
    </WidgetsProvider>
  );
}

export default App;
